<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 fw-bold">Generate Images</h1>
            <p class="text-muted">Create stunning AI-generated images using your prompts</p>
        </div>
    </div>

    <?php if (!$canGenerate): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Generation Limit Reached!</strong>
                    You have used all your generations for today.
                    <a href="<?= url('subscription') ?>" class="alert-link">Upgrade your plan</a> for more generations.
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (empty($apiKeys)): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="alert alert-info">
                    <i class="fas fa-key me-2"></i>
                    <strong>API Keys Required!</strong>
                    You need to configure your AI provider API keys before generating images.
                    <a href="<?= url('api-keys') ?>" class="alert-link">Configure API Keys</a>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Generation Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Single Image Generation</h5>
                </div>
                <div class="card-body p-4">
                    <form id="generation-form" method="POST" action="<?= url('generate') ?>">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <!-- Prompt -->
                        <div class="mb-4">
                            <label for="prompt" class="form-label fw-bold">Prompt *</label>
                            <textarea class="form-control"
                                      id="prompt"
                                      name="prompt"
                                      rows="3"
                                      maxlength="1000"
                                      placeholder="Describe the image you want to generate..."
                                      required><?= htmlspecialchars(old('prompt')) ?></textarea>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">Be specific and descriptive for better results</small>
                                <small id="prompt-counter" class="text-muted">0/1000</small>
                            </div>
                        </div>

                        <!-- Negative Prompt -->
                        <div class="mb-4">
                            <label for="negative_prompt" class="form-label fw-bold">Negative Prompt</label>
                            <textarea class="form-control"
                                      id="negative_prompt"
                                      name="negative_prompt"
                                      rows="2"
                                      placeholder="What you don't want in the image..."><?= htmlspecialchars(old('negative_prompt')) ?></textarea>
                            <small class="text-muted">Specify what to avoid in the generated image</small>
                        </div>

                        <!-- Dimensions -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Image Dimensions</label>
                            <div class="dimension-selector">
                                <input type="radio" class="btn-check" name="dimensions" id="dim_512x512" value="512x512" checked>
                                <label class="dimension-option" for="dim_512x512">512×512<br><small>Square</small></label>

                                <input type="radio" class="btn-check" name="dimensions" id="dim_768x768" value="768x768">
                                <label class="dimension-option" for="dim_768x768">768×768<br><small>Square HD</small></label>

                                <input type="radio" class="btn-check" name="dimensions" id="dim_1024x1024" value="1024x1024">
                                <label class="dimension-option" for="dim_1024x1024">1024×1024<br><small>Square FHD</small></label>

                                <input type="radio" class="btn-check" name="dimensions" id="dim_1024x768" value="1024x768">
                                <label class="dimension-option" for="dim_1024x768">1024×768<br><small>Landscape</small></label>

                                <input type="radio" class="btn-check" name="dimensions" id="dim_768x1024" value="768x1024">
                                <label class="dimension-option" for="dim_768x1024">768×1024<br><small>Portrait</small></label>
                            </div>

                            <!-- Hidden inputs for width/height -->
                            <input type="hidden" name="width" id="width" value="512">
                            <input type="hidden" name="height" id="height" value="512">
                        </div>

                        <!-- Style -->
                        <div class="mb-4">
                            <label for="style" class="form-label fw-bold">Style</label>
                            <select class="form-select" id="style" name="style">
                                <option value="">Default</option>
                                <option value="realistic">Realistic</option>
                                <option value="artistic">Artistic</option>
                                <option value="cartoon">Cartoon</option>
                                <option value="anime">Anime</option>
                                <option value="abstract">Abstract</option>
                                <option value="vintage">Vintage</option>
                                <option value="cyberpunk">Cyberpunk</option>
                            </select>
                        </div>

                        <!-- AI Provider -->
                        <div class="mb-4">
                            <label for="provider" class="form-label fw-bold">AI Provider *</label>
                            <select class="form-select" id="provider" name="provider" required>
                                <option value="">Select Provider</option>
                                <?php foreach ($apiKeys as $key): ?>
                                    <option value="<?= htmlspecialchars($key['provider']) ?>">
                                        <?= ucfirst(str_replace('_', ' ', $key['provider'])) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (empty($apiKeys)): ?>
                                <small class="text-muted">
                                    <a href="<?= url('api-keys') ?>">Configure API keys</a> to enable generation
                                </small>
                            <?php endif; ?>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="btn btn-primary btn-lg w-100"
                                <?= (!$canGenerate || empty($apiKeys)) ? 'disabled' : '' ?>>
                            <i class="fas fa-magic me-2"></i>Generate Image
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Usage Stats -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h6 class="mb-0">Usage Today</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Daily Limit</span>
                        <span class="fw-bold"><?= $usage['daily'] ?> / <?= $subscription['daily_generations'] ?? 5 ?></span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <?php
                        $dailyPercent = ($subscription['daily_generations'] ?? 5) > 0
                            ? ($usage['daily'] / ($subscription['daily_generations'] ?? 5)) * 100
                            : 0;
                        ?>
                        <div class="progress-bar" style="width: <?= min($dailyPercent, 100) ?>%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Monthly Limit</span>
                        <span class="fw-bold"><?= $usage['monthly'] ?> / <?= $subscription['monthly_generations'] ?? 50 ?></span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <?php
                        $monthlyPercent = ($subscription['monthly_generations'] ?? 50) > 0
                            ? ($usage['monthly'] / ($subscription['monthly_generations'] ?? 50)) * 100
                            : 0;
                        ?>
                        <div class="progress-bar bg-info" style="width: <?= min($monthlyPercent, 100) ?>%"></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= url('bulk-generate') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-layer-group me-2"></i>Bulk Generate
                        </a>
                        <a href="<?= url('gallery') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-images me-2"></i>View Gallery
                        </a>
                        <a href="<?= url('api-keys') ?>" class="btn btn-outline-info">
                            <i class="fas fa-key me-2"></i>Manage API Keys
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Generation Status (hidden by default) -->
    <div id="generation-status" class="row mt-4" style="display: none;">
        <div class="col">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6>Generation Status</h6>
                    <div class="progress mb-2">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                    </div>
                    <small id="status-text" class="text-muted">Initializing...</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Generation Result (hidden by default) -->
    <div id="generation-result" class="row mt-4" style="display: none;">
        <div class="col">
            <!-- Result will be populated by JavaScript -->
        </div>
    </div>
</div>

<script>
// Handle dimension selection
document.querySelectorAll('input[name="dimensions"]').forEach(input => {
    input.addEventListener('change', function() {
        const [width, height] = this.value.split('x');
        document.getElementById('width').value = width;
        document.getElementById('height').value = height;

        // Update visual selection
        document.querySelectorAll('.dimension-option').forEach(label => {
            label.classList.remove('active');
        });
        this.nextElementSibling.classList.add('active');
    });
});

// Initialize first option as active
document.querySelector('.dimension-option').classList.add('active');

// Initialize image generator
document.addEventListener('DOMContentLoaded', function() {
    ImageGenerator.initGenerationForm();
});
</script>

<?php
$content = ob_get_clean();
$title = 'Generate Images';
include __DIR__ . '/../layout/app.php';
?>
