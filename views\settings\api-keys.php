<?php
ob_start();
?>

<div class="container py-4">
    <div class="row">
        <div class="col-md-3">
            <!-- Settings Navigation -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="fw-bold mb-3">Settings</h6>
                    <nav class="nav nav-pills flex-column">
                        <a class="nav-link" href="/settings">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <a class="nav-link active" href="/api-keys">
                            <i class="fas fa-key me-2"></i>API Keys
                        </a>
                        <a class="nav-link" href="/subscription">
                            <i class="fas fa-credit-card me-2"></i>Subscription
                        </a>
                    </nav>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <!-- API Keys Info -->
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>About API Keys
                </h6>
                <p class="mb-0">
                    Configure your own API keys from AI providers to generate images. Your keys are encrypted and stored securely. 
                    You maintain full control over your usage and costs.
                </p>
            </div>
            
            <!-- Add New API Key -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Add API Key</h5>
                </div>
                <div class="card-body">
                    <form id="api-key-form" method="POST" action="/api-keys">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="provider" class="form-label">AI Provider</label>
                                <select class="form-select" id="provider" name="provider" required>
                                    <option value="">Select Provider</option>
                                    <?php foreach ($supportedProviders as $provider): ?>
                                        <option value="<?= htmlspecialchars($provider) ?>">
                                            <?= ucfirst(str_replace('_', ' ', $provider)) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="api_key" class="form-label">API Key</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="api_key" 
                                       name="api_key" 
                                       placeholder="Enter your API key"
                                       required>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add API Key
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Existing API Keys -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Your API Keys</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($apiKeys)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-key fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No API Keys Configured</h6>
                            <p class="text-muted">Add your first API key above to start generating images.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>Status</th>
                                        <th>Last Used</th>
                                        <th>Added</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($apiKeys as $key): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="provider-icon me-2">
                                                        <?php
                                                        $icons = [
                                                            'together_ai' => 'fas fa-brain',
                                                            'runware' => 'fas fa-bolt',
                                                            'replicate' => 'fas fa-copy',
                                                            'fal' => 'fas fa-magic'
                                                        ];
                                                        $icon = $icons[$key['provider']] ?? 'fas fa-cog';
                                                        ?>
                                                        <i class="<?= $icon ?> text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">
                                                            <?= ucfirst(str_replace('_', ' ', $key['provider'])) ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($key['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($key['last_used']): ?>
                                                    <?= time_ago($key['last_used']) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= date('M j, Y', strtotime($key['created_at'])) ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" 
                                                            class="btn btn-outline-primary test-api-key"
                                                            data-provider="<?= htmlspecialchars($key['provider']) ?>"
                                                            data-key-id="<?= $key['id'] ?>">
                                                        <i class="fas fa-check me-1"></i>Test
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger"
                                                            onclick="deleteApiKey(<?= $key['id'] ?>, '<?= htmlspecialchars($key['provider']) ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Provider Documentation -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">How to Get API Keys</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="fw-bold">
                                    <i class="fas fa-brain text-primary me-2"></i>Together AI
                                </h6>
                                <p class="small text-muted mb-2">
                                    High-quality image generation with various models.
                                </p>
                                <a href="https://together.ai" target="_blank" class="btn btn-sm btn-outline-primary">
                                    Get API Key <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="fw-bold">
                                    <i class="fas fa-bolt text-warning me-2"></i>Runware
                                </h6>
                                <p class="small text-muted mb-2">
                                    Fast and efficient AI image generation.
                                </p>
                                <a href="https://runware.ai" target="_blank" class="btn btn-sm btn-outline-primary">
                                    Get API Key <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="fw-bold">
                                    <i class="fas fa-copy text-success me-2"></i>Replicate
                                </h6>
                                <p class="small text-muted mb-2">
                                    Access to various AI models and tools.
                                </p>
                                <a href="https://replicate.com" target="_blank" class="btn btn-sm btn-outline-primary">
                                    Get API Key <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="fw-bold">
                                    <i class="fas fa-magic text-info me-2"></i>FAL AI
                                </h6>
                                <p class="small text-muted mb-2">
                                    Advanced AI image generation platform.
                                </p>
                                <a href="https://fal.ai" target="_blank" class="btn btn-sm btn-outline-primary">
                                    Get API Key <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteApiKey(keyId, provider) {
    if (confirm(`Are you sure you want to delete the API key for ${provider}?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/api-keys/${keyId}`;
        
        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Initialize API key manager
document.addEventListener('DOMContentLoaded', function() {
    ApiKeyManager.initApiKeyForm();
});
</script>

<?php
$content = ob_get_clean();
$title = 'API Keys';
include __DIR__ . '/../layout/app.php';
?>
