<?php

class AdminController extends BaseController {

    public function index() {
        Auth::requireAdmin();

        $db = Database::getInstance();

        // Get key metrics
        $metrics = $this->getDashboardMetrics();

        // Get recent activity
        $stmt = $db->query("
            SELECT u.first_name, u.last_name, u.email, u.created_at, u.last_login, u.status
            FROM users u
            WHERE u.role = 'user'
            ORDER BY u.created_at DESC
            LIMIT 10
        ");
        $recentUsers = $stmt->fetchAll();

        // Get recent images
        $stmt = $db->query("
            SELECT gi.*, u.first_name, u.last_name
            FROM generated_images gi
            JOIN users u ON gi.user_id = u.id
            ORDER BY gi.created_at DESC
            LIMIT 10
        ");
        $recentImages = $stmt->fetchAll();

        $this->view('admin/index', [
            'metrics' => $metrics,
            'recentUsers' => $recentUsers,
            'recentImages' => $recentImages
        ]);
    }

    public function users() {
        Auth::requireAdmin();

        $db = Database::getInstance();
        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = 20;
        $offset = ($page - 1) * $perPage;

        $search = sanitize($_GET['search'] ?? '');
        $status = sanitize($_GET['status'] ?? '');

        // Build query
        $whereConditions = ["role = 'user'"];
        $params = [];

        if (!empty($search)) {
            $whereConditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        if (!empty($status)) {
            $whereConditions[] = "status = ?";
            $params[] = $status;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Get total count
        $countQuery = "SELECT COUNT(*) FROM users $whereClause";
        $stmt = $db->prepare($countQuery);
        $stmt->execute($params);
        $totalUsers = $stmt->fetchColumn();

        // Get users with subscription info
        $query = "
            SELECT u.*,
                   sp.name as plan_name,
                   us.status as subscription_status,
                   COUNT(gi.id) as total_images
            FROM users u
            LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
            LEFT JOIN subscription_plans sp ON us.plan_id = sp.id
            LEFT JOIN generated_images gi ON u.id = gi.user_id
            $whereClause
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT $perPage OFFSET $offset
        ";
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $users = $stmt->fetchAll();

        $totalPages = ceil($totalUsers / $perPage);

        $this->view('admin/users', [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_users' => $totalUsers
            ],
            'filters' => [
                'search' => $search,
                'status' => $status
            ]
        ]);
    }

    public function userDetails($id) {
        Auth::requireAdmin();

        $db = Database::getInstance();

        // Get user details
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        $user = $stmt->fetch();

        if (!$user) {
            flash('error', 'User not found.');
            $this->redirect(url('admin/users'));
        }

        // Get subscription info
        $subscription = Auth::getUserSubscription($id);

        // Get usage stats
        $usage = Auth::getUserUsage($id);

        // Get recent images
        $stmt = $db->prepare("
            SELECT * FROM generated_images
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 20
        ");
        $stmt->execute([$id]);
        $recentImages = $stmt->fetchAll();

        // Get API keys count
        $stmt = $db->prepare("SELECT COUNT(*) FROM user_ai_provider_keys WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$id]);
        $apiKeysCount = $stmt->fetchColumn();

        $this->view('admin/user-details', [
            'user' => $user,
            'subscription' => $subscription,
            'usage' => $usage,
            'recentImages' => $recentImages,
            'apiKeysCount' => $apiKeysCount
        ]);
    }

    public function suspendUser($id) {
        Auth::requireAdmin();
        $this->validateCsrf();

        $this->updateUserStatus($id, 'suspended');
        flash('success', 'User suspended successfully.');
        $this->redirect('/admin/users/' . $id);
    }

    public function activateUser($id) {
        Auth::requireAdmin();
        $this->validateCsrf();

        $this->updateUserStatus($id, 'active');
        flash('success', 'User activated successfully.');
        $this->redirect('/admin/users/' . $id);
    }

    public function analytics() {
        Auth::requireAdmin();

        $db = Database::getInstance();

        // Get analytics data
        $analytics = [
            'users' => $this->getUserAnalytics(),
            'revenue' => $this->getRevenueAnalytics(),
            'usage' => $this->getUsageAnalytics(),
            'providers' => $this->getProviderAnalytics()
        ];

        $this->view('admin/analytics', [
            'analytics' => $analytics
        ]);
    }

    public function settings() {
        Auth::requireAdmin();

        $db = Database::getInstance();

        // Get current settings
        $stmt = $db->query("SELECT * FROM system_settings ORDER BY setting_key");
        $settings = $stmt->fetchAll();

        $settingsArray = [];
        foreach ($settings as $setting) {
            $settingsArray[$setting['setting_key']] = $setting['setting_value'];
        }

        $this->view('admin/settings', [
            'settings' => $settingsArray
        ]);
    }

    public function saveSettings() {
        Auth::requireAdmin();
        $this->validateCsrf();

        $db = Database::getInstance();

        try {
            $db->beginTransaction();

            foreach ($_POST as $key => $value) {
                if ($key === 'csrf_token') continue;

                $stmt = $db->prepare("
                    INSERT OR REPLACE INTO system_settings (setting_key, setting_value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ");
                $stmt->execute([$key, sanitize($value)]);
            }

            $db->commit();

            flash('success', 'Settings updated successfully.');
            log_activity(Auth::id(), 'admin_settings_updated');

        } catch (Exception $e) {
            $db->rollback();
            log_error('Admin settings update failed: ' . $e->getMessage());
            flash('error', 'Failed to update settings. Please try again.');
        }

        $this->redirect(url('admin/settings'));
    }

    public function plans() {
        Auth::requireAdmin();

        $db = Database::getInstance();

        // Get all plans
        $stmt = $db->query("SELECT * FROM subscription_plans ORDER BY price ASC");
        $plans = $stmt->fetchAll();

        // Get plan usage statistics
        foreach ($plans as &$plan) {
            $stmt = $db->prepare("
                SELECT COUNT(*) as subscriber_count
                FROM user_subscriptions us
                WHERE us.plan_id = ? AND us.status = 'active'
            ");
            $stmt->execute([$plan['id']]);
            $plan['subscriber_count'] = $stmt->fetchColumn();
        }

        $this->view('admin/plans', [
            'plans' => $plans
        ]);
    }

    public function savePlan() {
        Auth::requireAdmin();
        $this->validateCsrf();

        $data = [
            'name' => sanitize($_POST['name'] ?? ''),
            'description' => sanitize($_POST['description'] ?? ''),
            'price' => (float)($_POST['price'] ?? 0),
            'billing_cycle' => sanitize($_POST['billing_cycle'] ?? 'monthly'),
            'daily_generations' => (int)($_POST['daily_generations'] ?? 0),
            'monthly_generations' => (int)($_POST['monthly_generations'] ?? 0),
            'max_bulk_size' => (int)($_POST['max_bulk_size'] ?? 0),
            'features' => json_encode(array_filter(explode("\n", $_POST['features'] ?? ''))),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        $errors = $this->validate($data, [
            'name' => 'required|min:2|max:100',
            'price' => 'required',
            'billing_cycle' => 'required',
            'daily_generations' => 'required',
            'monthly_generations' => 'required'
        ]);

        if (!empty($errors)) {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    flash('error', $error);
                }
            }
            $this->redirect(url('admin/plans'));
        }

        try {
            $db = Database::getInstance();

            $planId = (int)($_POST['plan_id'] ?? 0);

            if ($planId > 0) {
                // Update existing plan
                $stmt = $db->prepare("
                    UPDATE subscription_plans
                    SET name = ?, description = ?, price = ?, billing_cycle = ?,
                        daily_generations = ?, monthly_generations = ?, max_bulk_size = ?,
                        features = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([
                    $data['name'], $data['description'], $data['price'], $data['billing_cycle'],
                    $data['daily_generations'], $data['monthly_generations'], $data['max_bulk_size'],
                    $data['features'], $data['is_active'], $planId
                ]);

                flash('success', 'Plan updated successfully.');
            } else {
                // Create new plan
                $stmt = $db->prepare("
                    INSERT INTO subscription_plans (name, description, price, billing_cycle, daily_generations, monthly_generations, max_bulk_size, features, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $data['name'], $data['description'], $data['price'], $data['billing_cycle'],
                    $data['daily_generations'], $data['monthly_generations'], $data['max_bulk_size'],
                    $data['features'], $data['is_active']
                ]);

                flash('success', 'Plan created successfully.');
            }

            log_activity(Auth::id(), 'admin_plan_saved', ['plan_name' => $data['name']]);

        } catch (Exception $e) {
            log_error('Plan save failed: ' . $e->getMessage(), $data);
            flash('error', 'Failed to save plan. Please try again.');
        }

        $this->redirect(url('admin/plans'));
    }

    private function getDashboardMetrics() {
        $db = Database::getInstance();

        $metrics = [];

        // Total users
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE role = 'user'");
        $metrics['total_users'] = $stmt->fetchColumn();

        // Active users (logged in last 30 days)
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE role = 'user' AND last_login > DATE('now', '-30 days')");
        $metrics['active_users'] = $stmt->fetchColumn();

        // Total images generated
        $stmt = $db->query("SELECT COUNT(*) FROM generated_images WHERE status = 'completed'");
        $metrics['total_images'] = $stmt->fetchColumn();

        // Images generated today
        $stmt = $db->query("SELECT COUNT(*) FROM generated_images WHERE status = 'completed' AND DATE(created_at) = DATE('now')");
        $metrics['images_today'] = $stmt->fetchColumn();

        // Total revenue (simplified)
        $stmt = $db->query("
            SELECT SUM(sp.price)
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_id = sp.id
            WHERE us.status = 'active' AND sp.price > 0
        ");
        $metrics['monthly_revenue'] = $stmt->fetchColumn() ?: 0;

        // Active subscriptions
        $stmt = $db->query("SELECT COUNT(*) FROM user_subscriptions WHERE status = 'active'");
        $metrics['active_subscriptions'] = $stmt->fetchColumn();

        return $metrics;
    }

    private function updateUserStatus($userId, $status) {
        $db = Database::getInstance();

        $stmt = $db->prepare("UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$status, $userId]);

        log_activity(Auth::id(), 'admin_user_status_changed', ['user_id' => $userId, 'status' => $status]);
    }

    private function getUserAnalytics() {
        $db = Database::getInstance();

        // User registrations over time (last 30 days)
        $stmt = $db->query("
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM users
            WHERE role = 'user' AND created_at > DATE('now', '-30 days')
            GROUP BY DATE(created_at)
            ORDER BY date
        ");

        return $stmt->fetchAll();
    }

    private function getRevenueAnalytics() {
        $db = Database::getInstance();

        // Revenue by plan
        $stmt = $db->query("
            SELECT sp.name, sp.price, COUNT(us.id) as subscribers, (sp.price * COUNT(us.id)) as revenue
            FROM subscription_plans sp
            LEFT JOIN user_subscriptions us ON sp.id = us.plan_id AND us.status = 'active'
            WHERE sp.is_active = 1
            GROUP BY sp.id
            ORDER BY revenue DESC
        ");

        return $stmt->fetchAll();
    }

    private function getUsageAnalytics() {
        $db = Database::getInstance();

        // Image generation by provider
        $stmt = $db->query("
            SELECT ai_provider, COUNT(*) as count
            FROM generated_images
            WHERE status = 'completed'
            GROUP BY ai_provider
            ORDER BY count DESC
        ");

        return $stmt->fetchAll();
    }

    private function getProviderAnalytics() {
        $db = Database::getInstance();

        // API key usage by provider
        $stmt = $db->query("
            SELECT provider, COUNT(*) as users_count
            FROM user_ai_provider_keys
            WHERE is_active = 1
            GROUP BY provider
            ORDER BY users_count DESC
        ");

        return $stmt->fetchAll();
    }
}
