<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 fw-bold">Bulk Image Generation</h1>
            <p class="text-muted">Generate multiple images at once using prompts from a file or text input</p>
        </div>
    </div>
    
    <?php if (empty($apiKeys)): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="alert alert-info">
                    <i class="fas fa-key me-2"></i>
                    <strong>API Keys Required!</strong> 
                    You need to configure your AI provider API keys before generating images.
                    <a href="<?= url('api-keys') ?>" class="alert-link">Configure API Keys</a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Bulk Generation Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Bulk Generation Settings</h5>
                </div>
                <div class="card-body p-4">
                    <form id="bulk-generation-form" method="POST" action="<?= url('bulk-generate') ?>" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                        
                        <!-- Prompts Input Method -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Prompts Input Method</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="input_method" id="method_file" value="file" checked>
                                        <label class="form-check-label" for="method_file">
                                            <strong>Upload File</strong><br>
                                            <small class="text-muted">Upload a text file or CSV with prompts</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="input_method" id="method_text" value="text">
                                        <label class="form-check-label" for="method_text">
                                            <strong>Manual Input</strong><br>
                                            <small class="text-muted">Type prompts directly (one per line)</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- File Upload -->
                        <div id="file-input-section" class="mb-4">
                            <label for="prompts_file" class="form-label fw-bold">Upload Prompts File</label>
                            <input type="file" class="form-control" id="prompts_file" name="prompts_file" accept=".txt,.csv">
                            <div class="form-text">
                                Supported formats: .txt (one prompt per line) or .csv (prompts in first column)
                            </div>
                            <div id="file-preview" class="mt-2"></div>
                        </div>
                        
                        <!-- Text Input -->
                        <div id="text-input-section" class="mb-4" style="display: none;">
                            <label for="prompts_text" class="form-label fw-bold">Prompts (one per line)</label>
                            <textarea class="form-control" 
                                      id="prompts_text" 
                                      name="prompts_text" 
                                      rows="8" 
                                      placeholder="Enter your prompts here, one per line..."></textarea>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">Maximum <?= $maxBulkSize ?> prompts allowed for your plan</small>
                                <small id="prompts-counter" class="text-muted">0 prompts</small>
                            </div>
                        </div>
                        
                        <!-- Dimensions -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Image Dimensions</label>
                            <div class="dimension-selector">
                                <input type="radio" class="btn-check" name="dimensions" id="bulk_dim_512x512" value="512x512" checked>
                                <label class="dimension-option" for="bulk_dim_512x512">512×512<br><small>Square</small></label>
                                
                                <input type="radio" class="btn-check" name="dimensions" id="bulk_dim_768x768" value="768x768">
                                <label class="dimension-option" for="bulk_dim_768x768">768×768<br><small>Square HD</small></label>
                                
                                <input type="radio" class="btn-check" name="dimensions" id="bulk_dim_1024x1024" value="1024x1024">
                                <label class="dimension-option" for="bulk_dim_1024x1024">1024×1024<br><small>Square FHD</small></label>
                                
                                <input type="radio" class="btn-check" name="dimensions" id="bulk_dim_1024x768" value="1024x768">
                                <label class="dimension-option" for="bulk_dim_1024x768">1024×768<br><small>Landscape</small></label>
                                
                                <input type="radio" class="btn-check" name="dimensions" id="bulk_dim_768x1024" value="768x1024">
                                <label class="dimension-option" for="bulk_dim_768x1024">768×1024<br><small>Portrait</small></label>
                            </div>
                            
                            <!-- Hidden inputs for width/height -->
                            <input type="hidden" name="width" id="bulk_width" value="512">
                            <input type="hidden" name="height" id="bulk_height" value="512">
                        </div>
                        
                        <!-- Style -->
                        <div class="mb-4">
                            <label for="bulk_style" class="form-label fw-bold">Style</label>
                            <select class="form-select" id="bulk_style" name="style">
                                <option value="">Default</option>
                                <option value="realistic">Realistic</option>
                                <option value="artistic">Artistic</option>
                                <option value="cartoon">Cartoon</option>
                                <option value="anime">Anime</option>
                                <option value="abstract">Abstract</option>
                                <option value="vintage">Vintage</option>
                                <option value="cyberpunk">Cyberpunk</option>
                            </select>
                        </div>
                        
                        <!-- AI Provider -->
                        <div class="mb-4">
                            <label for="bulk_provider" class="form-label fw-bold">AI Provider *</label>
                            <select class="form-select" id="bulk_provider" name="provider" required>
                                <option value="">Select Provider</option>
                                <?php foreach ($apiKeys as $key): ?>
                                    <option value="<?= htmlspecialchars($key['provider']) ?>">
                                        <?= ucfirst(str_replace('_', ' ', $key['provider'])) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (empty($apiKeys)): ?>
                                <small class="text-muted">
                                    <a href="<?= url('api-keys') ?>">Configure API keys</a> to enable generation
                                </small>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" 
                                class="btn btn-primary btn-lg w-100" 
                                <?= empty($apiKeys) ? 'disabled' : '' ?>>
                            <i class="fas fa-layer-group me-2"></i>Start Bulk Generation
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Plan Limits -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h6 class="mb-0">Bulk Generation Limits</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Max Bulk Size</span>
                            <span class="fw-bold"><?= $maxBulkSize ?> images</span>
                        </div>
                        <small class="text-muted">Maximum number of images you can generate in one batch</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Current Plan</span>
                            <span class="fw-bold"><?= htmlspecialchars($subscription['name'] ?? 'Free') ?></span>
                        </div>
                        <small class="text-muted">
                            <a href="<?= url('subscription') ?>">Upgrade plan</a> for higher limits
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Tips -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h6 class="mb-0">Bulk Generation Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use consistent prompts for better results</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file-alt text-info me-2"></i>
                            <small>CSV format: put prompts in the first column</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            <small>Bulk generation may take several minutes</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-images text-success me-2"></i>
                            <small>Check your gallery for completed images</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle input method selection
document.querySelectorAll('input[name="input_method"]').forEach(input => {
    input.addEventListener('change', function() {
        const fileSection = document.getElementById('file-input-section');
        const textSection = document.getElementById('text-input-section');
        
        if (this.value === 'file') {
            fileSection.style.display = 'block';
            textSection.style.display = 'none';
        } else {
            fileSection.style.display = 'none';
            textSection.style.display = 'block';
        }
    });
});

// Handle dimension selection for bulk
document.querySelectorAll('input[name="dimensions"]').forEach(input => {
    input.addEventListener('change', function() {
        const [width, height] = this.value.split('x');
        document.getElementById('bulk_width').value = width;
        document.getElementById('bulk_height').value = height;
        
        // Update visual selection
        document.querySelectorAll('.dimension-option').forEach(label => {
            label.classList.remove('active');
        });
        this.nextElementSibling.classList.add('active');
    });
});

// Initialize first option as active
document.querySelector('.dimension-option').classList.add('active');

// Count prompts in text area
const promptsTextarea = document.getElementById('prompts_text');
const promptsCounter = document.getElementById('prompts-counter');

if (promptsTextarea && promptsCounter) {
    promptsTextarea.addEventListener('input', function() {
        const lines = this.value.split('\n').filter(line => line.trim().length > 0);
        promptsCounter.textContent = `${lines.length} prompts`;
        
        if (lines.length > <?= $maxBulkSize ?>) {
            promptsCounter.classList.add('text-danger');
        } else {
            promptsCounter.classList.remove('text-danger');
        }
    });
}

// File upload preview
document.getElementById('prompts_file').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('file-preview');
    
    if (file) {
        preview.innerHTML = `
            <div class="alert alert-info mb-0">
                <i class="fas fa-file me-2"></i>
                <strong>${file.name}</strong> (${Utils.formatFileSize(file.size)})
            </div>
        `;
    } else {
        preview.innerHTML = '';
    }
});
</script>

<?php
$content = ob_get_clean();
$title = 'Bulk Generation';
include __DIR__ . '/../layout/app.php';
?>
