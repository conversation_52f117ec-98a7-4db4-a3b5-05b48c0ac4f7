<?php

class HomeController extends BaseController {

    public function index() {
        // If user is logged in, redirect to dashboard
        if (Auth::check()) {
            $this->redirect(url('dashboard'));
        }

        // Get some statistics for the landing page
        $db = Database::getInstance();

        // Total images generated
        $stmt = $db->query("SELECT COUNT(*) FROM generated_images WHERE status = 'completed'");
        $totalImages = $stmt->fetchColumn();

        // Total users
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
        $totalUsers = $stmt->fetchColumn();

        // Get subscription plans for pricing table
        $stmt = $db->query("SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC");
        $plans = $stmt->fetchAll();

        // Get recent sample images (if any)
        $stmt = $db->query("
            SELECT file_path, prompt, width, height
            FROM generated_images
            WHERE status = 'completed'
            ORDER BY created_at DESC
            LIMIT 6
        ");
        $sampleImages = $stmt->fetchAll();

        $this->view('home/index', [
            'totalImages' => $totalImages,
            'totalUsers' => $totalUsers,
            'plans' => $plans,
            'sampleImages' => $sampleImages
        ]);
    }
}
