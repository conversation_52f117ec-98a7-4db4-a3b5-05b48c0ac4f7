<?php
session_start();

// Include configuration and autoloader
require_once 'config/app.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/router.php';

// Load configuration
Config::load();

// Enable router debugging temporarily (remove in production)
// define('DEBUG_ROUTER', true);

// Initialize database
$db = Database::getInstance();

// Initialize router
$router = new Router();

// Define routes
$router->get('/', 'HomeController@index');
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/register', 'AuthController@showRegister');
$router->post('/register', 'AuthController@register');
$router->get('/logout', 'AuthController@logout');
$router->get('/verify-email', 'AuthController@verifyEmail');
$router->get('/forgot-password', 'AuthController@showForgotPassword');
$router->post('/forgot-password', 'AuthController@forgotPassword');
$router->get('/reset-password', 'AuthController@showResetPassword');
$router->post('/reset-password', 'AuthController@resetPassword');

// Dashboard routes (require authentication)
$router->get('/dashboard', 'DashboardController@index');
$router->get('/generate', 'GenerateController@index');
$router->post('/generate', 'GenerateController@single');
$router->get('/bulk-generate', 'GenerateController@bulk');
$router->post('/bulk-generate', 'GenerateController@processBulk');
$router->get('/gallery', 'GalleryController@index');
$router->get('/gallery/{id}', 'GalleryController@show');
$router->post('/gallery/{id}/delete', 'GalleryController@delete');
$router->post('/gallery/bulk-delete', 'GalleryController@bulkDelete');
$router->post('/gallery/{id}/regenerate', 'GalleryController@regenerate');
$router->get('/settings', 'SettingsController@index');
$router->post('/settings', 'SettingsController@update');
$router->post('/settings/change-password', 'SettingsController@changePassword');
$router->post('/settings/delete-account', 'SettingsController@deleteAccount');
$router->get('/api-keys', 'SettingsController@apiKeys');
$router->post('/api-keys', 'SettingsController@saveApiKey');
$router->delete('/api-keys/{id}', 'SettingsController@deleteApiKey');
$router->get('/subscription', 'SubscriptionController@index');
$router->post('/subscription/upgrade', 'SubscriptionController@upgrade');
$router->post('/subscription/cancel', 'SubscriptionController@cancel');
$router->post('/subscription/resume', 'SubscriptionController@resume');
$router->get('/subscription/success', 'SubscriptionController@success');
$router->get('/subscription/cancel-page', 'SubscriptionController@cancel_page');

// Admin routes (require admin role)
$router->get('/admin', 'AdminController@index');
$router->get('/admin/users', 'AdminController@users');
$router->get('/admin/users/{id}', 'AdminController@userDetails');
$router->post('/admin/users/{id}/suspend', 'AdminController@suspendUser');
$router->post('/admin/users/{id}/activate', 'AdminController@activateUser');
$router->get('/admin/plans', 'AdminController@plans');
$router->post('/admin/plans', 'AdminController@savePlan');
$router->get('/admin/settings', 'AdminController@settings');
$router->post('/admin/settings', 'AdminController@saveSettings');
$router->get('/admin/analytics', 'AdminController@analytics');

// API routes
$router->get('/api/usage', 'ApiController@usage');
$router->post('/api/generate', 'ApiController@generate');
$router->get('/api/images', 'ApiController@images');
$router->get('/api/images/{id}/status', 'ApiController@imageStatus');
$router->post('/api/test-key', 'ApiController@testKey');

// File download routes
$router->get('/download/{id}', 'FileController@download');
$router->get('/download/bulk/{batch_id}', 'FileController@downloadBulk');
$router->get('/serve/{id}', 'FileController@serve');
$router->post('/upload/prompts', 'FileController@uploadPrompts');

// Webhook routes
$router->post('/webhook/stripe', 'WebhookController@stripe');
$router->post('/webhook/ai/{provider}', 'WebhookController@aiProvider');

// Legal pages
$router->get('/privacy', 'HomeController@privacy');
$router->get('/terms', 'HomeController@terms');
$router->get('/contact', 'HomeController@contact');
$router->post('/contact', 'HomeController@submitContact');

// Handle the request
try {
    $router->dispatch();
} catch (Exception $e) {
    if (Config::get('app.debug')) {
        echo "<h1>Error</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        http_response_code(500);
        include 'views/errors/500.php';
    }
}
