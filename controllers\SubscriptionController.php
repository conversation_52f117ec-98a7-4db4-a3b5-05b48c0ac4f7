<?php

class SubscriptionController extends BaseController {
    
    public function index() {
        Auth::requireAuth();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        // Get current subscription
        $subscription = Auth::getUserSubscription($userId);
        
        // Get all available plans
        $stmt = $db->query("SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC");
        $plans = $stmt->fetchAll();
        
        // Get usage statistics
        $usage = Auth::getUserUsage($userId);
        
        // Get billing history
        $stmt = $db->prepare("
            SELECT us.*, sp.name as plan_name, sp.price 
            FROM user_subscriptions us 
            JOIN subscription_plans sp ON us.plan_id = sp.id 
            WHERE us.user_id = ? 
            ORDER BY us.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $billingHistory = $stmt->fetchAll();
        
        $this->view('subscription/index', [
            'subscription' => $subscription,
            'plans' => $plans,
            'usage' => $usage,
            'billingHistory' => $billingHistory
        ]);
    }
    
    public function upgrade() {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        $planId = (int)($_POST['plan_id'] ?? 0);
        
        if (empty($planId)) {
            flash('error', 'Please select a plan.');
            $this->redirect('/subscription');
        }
        
        $db = Database::getInstance();
        
        try {
            // Get plan details
            $stmt = $db->prepare("SELECT * FROM subscription_plans WHERE id = ? AND is_active = 1");
            $stmt->execute([$planId]);
            $plan = $stmt->fetch();
            
            if (!$plan) {
                flash('error', 'Invalid plan selected.');
                $this->redirect('/subscription');
            }
            
            // Get current subscription
            $currentSubscription = Auth::getUserSubscription($userId);
            
            // If it's a free plan, just update directly
            if ($plan['price'] == 0) {
                $this->updateSubscription($userId, $planId, 'active');
                flash('success', 'Successfully switched to ' . $plan['name'] . ' plan!');
                $this->redirect('/subscription');
            }
            
            // For paid plans, redirect to Stripe checkout
            $this->createStripeCheckoutSession($userId, $plan);
            
        } catch (Exception $e) {
            log_error('Subscription upgrade failed: ' . $e->getMessage(), ['plan_id' => $planId]);
            flash('error', 'Failed to process subscription. Please try again.');
            $this->redirect('/subscription');
        }
    }
    
    public function cancel() {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        try {
            $subscription = Auth::getUserSubscription($userId);
            
            if (!$subscription || $subscription['status'] !== 'active') {
                flash('error', 'No active subscription found.');
                $this->redirect('/subscription');
            }
            
            // If it's a Stripe subscription, cancel it
            if (!empty($subscription['stripe_subscription_id'])) {
                $this->cancelStripeSubscription($subscription['stripe_subscription_id']);
            }
            
            // Update subscription to cancel at period end
            $stmt = $db->prepare("
                UPDATE user_subscriptions 
                SET cancel_at_period_end = 1, updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);
            
            flash('success', 'Subscription will be canceled at the end of your current billing period.');
            log_activity($userId, 'subscription_canceled');
            
        } catch (Exception $e) {
            log_error('Subscription cancellation failed: ' . $e->getMessage());
            flash('error', 'Failed to cancel subscription. Please try again.');
        }
        
        $this->redirect('/subscription');
    }
    
    public function resume() {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        try {
            $subscription = Auth::getUserSubscription($userId);
            
            if (!$subscription || !$subscription['cancel_at_period_end']) {
                flash('error', 'No canceled subscription found.');
                $this->redirect('/subscription');
            }
            
            // If it's a Stripe subscription, resume it
            if (!empty($subscription['stripe_subscription_id'])) {
                $this->resumeStripeSubscription($subscription['stripe_subscription_id']);
            }
            
            // Update subscription
            $stmt = $db->prepare("
                UPDATE user_subscriptions 
                SET cancel_at_period_end = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);
            
            flash('success', 'Subscription resumed successfully!');
            log_activity($userId, 'subscription_resumed');
            
        } catch (Exception $e) {
            log_error('Subscription resume failed: ' . $e->getMessage());
            flash('error', 'Failed to resume subscription. Please try again.');
        }
        
        $this->redirect('/subscription');
    }
    
    public function success() {
        Auth::requireAuth();
        
        $sessionId = $_GET['session_id'] ?? '';
        
        if (empty($sessionId)) {
            flash('error', 'Invalid session.');
            $this->redirect('/subscription');
        }
        
        try {
            // Verify Stripe session and update subscription
            $this->handleStripeSuccess($sessionId);
            
            flash('success', 'Subscription activated successfully! Welcome to your new plan!');
            
        } catch (Exception $e) {
            log_error('Subscription success handling failed: ' . $e->getMessage());
            flash('error', 'There was an issue activating your subscription. Please contact support.');
        }
        
        $this->redirect('/subscription');
    }
    
    public function cancel_page() {
        Auth::requireAuth();
        
        flash('info', 'Subscription upgrade was canceled. You can try again anytime.');
        $this->redirect('/subscription');
    }
    
    private function updateSubscription($userId, $planId, $status = 'active', $stripeSubscriptionId = null) {
        $db = Database::getInstance();
        
        try {
            $db->beginTransaction();
            
            // Deactivate current subscription
            $stmt = $db->prepare("UPDATE user_subscriptions SET status = 'canceled' WHERE user_id = ? AND status = 'active'");
            $stmt->execute([$userId]);
            
            // Create new subscription
            $stmt = $db->prepare("
                INSERT INTO user_subscriptions (user_id, plan_id, stripe_subscription_id, status, current_period_start, current_period_end) 
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, DATE('now', '+1 month'))
            ");
            $stmt->execute([$userId, $planId, $stripeSubscriptionId, $status]);
            
            $db->commit();
            
            log_activity($userId, 'subscription_updated', ['plan_id' => $planId, 'status' => $status]);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    private function createStripeCheckoutSession($userId, $plan) {
        // This is a simplified version - in production, you'd use the actual Stripe SDK
        
        $user = Auth::user();
        
        // Simulate Stripe checkout URL
        $checkoutUrl = Config::get('app.url') . '/subscription/success?session_id=sim_' . uniqid();
        
        // In real implementation:
        // $stripe = new \Stripe\StripeClient(Config::get('stripe.secret_key'));
        // $session = $stripe->checkout->sessions->create([...]);
        
        flash('info', 'Redirecting to payment processor...');
        $this->redirect($checkoutUrl);
    }
    
    private function cancelStripeSubscription($subscriptionId) {
        // In real implementation:
        // $stripe = new \Stripe\StripeClient(Config::get('stripe.secret_key'));
        // $stripe->subscriptions->update($subscriptionId, ['cancel_at_period_end' => true]);
        
        log_activity(Auth::id(), 'stripe_subscription_canceled', ['subscription_id' => $subscriptionId]);
    }
    
    private function resumeStripeSubscription($subscriptionId) {
        // In real implementation:
        // $stripe = new \Stripe\StripeClient(Config::get('stripe.secret_key'));
        // $stripe->subscriptions->update($subscriptionId, ['cancel_at_period_end' => false]);
        
        log_activity(Auth::id(), 'stripe_subscription_resumed', ['subscription_id' => $subscriptionId]);
    }
    
    private function handleStripeSuccess($sessionId) {
        $userId = Auth::id();
        
        // In real implementation, you'd retrieve the session from Stripe
        // and get the subscription details
        
        // For simulation, we'll upgrade to Pro plan
        $this->updateSubscription($userId, 2, 'active', 'sub_' . uniqid());
    }
    
    public function downloadInvoice($invoiceId) {
        Auth::requireAuth();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        // Verify invoice belongs to user
        $stmt = $db->prepare("
            SELECT us.*, sp.name as plan_name, sp.price 
            FROM user_subscriptions us 
            JOIN subscription_plans sp ON us.plan_id = sp.id 
            WHERE us.id = ? AND us.user_id = ?
        ");
        $stmt->execute([$invoiceId, $userId]);
        $invoice = $stmt->fetch();
        
        if (!$invoice) {
            flash('error', 'Invoice not found.');
            $this->redirect('/subscription');
        }
        
        // Generate PDF invoice (simplified)
        $this->generateInvoicePDF($invoice);
    }
    
    private function generateInvoicePDF($invoice) {
        // In real implementation, you'd use a PDF library like TCPDF or FPDF
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="invoice_' . $invoice['id'] . '.pdf"');
        
        // For now, just output a simple text representation
        echo "Invoice #" . $invoice['id'] . "\n";
        echo "Plan: " . $invoice['plan_name'] . "\n";
        echo "Amount: $" . number_format($invoice['price'], 2) . "\n";
        echo "Date: " . date('Y-m-d', strtotime($invoice['created_at'])) . "\n";
        
        exit;
    }
}
