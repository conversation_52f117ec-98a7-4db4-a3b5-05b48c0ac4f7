<?php

/**
 * Simple PHP Router
 */

class Router {
    private $routes = [];
    private $middlewares = [];

    public function get($path, $handler) {
        $this->addRoute('GET', $path, $handler);
    }

    public function post($path, $handler) {
        $this->addRoute('POST', $path, $handler);
    }

    public function put($path, $handler) {
        $this->addRoute('PUT', $path, $handler);
    }

    public function delete($path, $handler) {
        $this->addRoute('DELETE', $path, $handler);
    }

    private function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    public function dispatch() {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Remove base path if running in subdirectory
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/' && strpos($requestPath, $basePath) === 0) {
            $requestPath = substr($requestPath, strlen($basePath));
        }

        // Ensure path starts with /
        if (empty($requestPath) || $requestPath[0] !== '/') {
            $requestPath = '/' . $requestPath;
        }

        // Remove trailing slash except for root
        if ($requestPath !== '/' && substr($requestPath, -1) === '/') {
            $requestPath = rtrim($requestPath, '/');
        }

        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchPath($route['path'], $requestPath)) {
                $params = $this->extractParams($route['path'], $requestPath);
                return $this->callHandler($route['handler'], $params);
            }
        }

        // Route not found
        http_response_code(404);
        include 'views/errors/404.php';
    }

    private function matchPath($routePath, $requestPath) {
        // Convert route path to regex pattern
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';

        return preg_match($pattern, $requestPath);
    }

    private function extractParams($routePath, $requestPath) {
        $params = [];

        // Extract parameter names from route path
        preg_match_all('/\{([^}]+)\}/', $routePath, $paramNames);

        // Extract parameter values from request path
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $requestPath, $matches)) {
            array_shift($matches); // Remove full match

            foreach ($paramNames[1] as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }
        }

        return $params;
    }

    private function callHandler($handler, $params = []) {
        if (is_string($handler)) {
            // Handle Controller@method format
            if (strpos($handler, '@') !== false) {
                list($controller, $method) = explode('@', $handler);

                $controllerFile = "controllers/{$controller}.php";
                if (!file_exists($controllerFile)) {
                    throw new Exception("Controller file not found: $controllerFile");
                }

                require_once $controllerFile;

                if (!class_exists($controller)) {
                    throw new Exception("Controller class not found: $controller");
                }

                $controllerInstance = new $controller();

                if (!method_exists($controllerInstance, $method)) {
                    throw new Exception("Method not found: $controller::$method");
                }

                return call_user_func_array([$controllerInstance, $method], $params);
            }

            // Handle function name
            if (function_exists($handler)) {
                return call_user_func_array($handler, $params);
            }

            throw new Exception("Handler not found: $handler");
        }

        if (is_callable($handler)) {
            return call_user_func_array($handler, $params);
        }

        throw new Exception("Invalid handler type");
    }
}

/**
 * Base Controller class
 */
class BaseController {
    protected function view($template, $data = []) {
        extract($data);

        $templateFile = "views/$template.php";
        if (!file_exists($templateFile)) {
            throw new Exception("Template not found: $templateFile");
        }

        include $templateFile;
    }

    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }

    protected function redirect($url, $statusCode = 302) {
        redirect($url, $statusCode);
    }

    protected function validateCsrf() {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
        if (!verify_csrf_token($token)) {
            flash('error', 'Invalid security token. Please try again.');
            $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
        }
    }

    protected function validate($data, $rules) {
        $errors = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);

            foreach ($ruleList as $singleRule) {
                $ruleParts = explode(':', $singleRule);
                $ruleName = $ruleParts[0];
                $ruleValue = $ruleParts[1] ?? null;

                switch ($ruleName) {
                    case 'required':
                        if (empty($value)) {
                            $errors[$field][] = ucfirst($field) . ' is required.';
                        }
                        break;

                    case 'email':
                        if (!empty($value) && !validate_email($value)) {
                            $errors[$field][] = ucfirst($field) . ' must be a valid email address.';
                        }
                        break;

                    case 'min':
                        if (!empty($value) && strlen($value) < $ruleValue) {
                            $errors[$field][] = ucfirst($field) . " must be at least $ruleValue characters.";
                        }
                        break;

                    case 'max':
                        if (!empty($value) && strlen($value) > $ruleValue) {
                            $errors[$field][] = ucfirst($field) . " must not exceed $ruleValue characters.";
                        }
                        break;

                    case 'confirmed':
                        $confirmField = $field . '_confirmation';
                        if ($value !== ($data[$confirmField] ?? null)) {
                            $errors[$field][] = ucfirst($field) . ' confirmation does not match.';
                        }
                        break;

                    case 'unique':
                        if (!empty($value)) {
                            $table = $ruleValue;
                            $db = Database::getInstance();
                            $stmt = $db->prepare("SELECT COUNT(*) FROM $table WHERE $field = ?");
                            $stmt->execute([$value]);
                            if ($stmt->fetchColumn() > 0) {
                                $errors[$field][] = ucfirst($field) . ' is already taken.';
                            }
                        }
                        break;
                }
            }
        }

        return $errors;
    }
}
